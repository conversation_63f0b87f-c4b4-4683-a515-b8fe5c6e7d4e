using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DriverManagementSystem.ViewModels;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using OfficeOpenXml.Table;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    public partial class VehicleManagementWindow : Window
    {
        public VehicleManagementWindow()
        {
            try
            {
                InitializeComponent();
                DataContext = new VehicleManagementViewModel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة إدارة السيارات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseWindow_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void RefreshDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel)
                {
                    viewModel.LoadData();
                    MessageBox.Show("تم تحديث البيانات بنجاح", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportToExcelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel)
                {
                    System.Diagnostics.Debug.WriteLine($"📊 Export Excel clicked - FilteredDrivers count: {viewModel.FilteredDrivers.Count}");

                    if (viewModel.FilteredDrivers.Count == 0)
                    {
                        MessageBox.Show("لا توجد بيانات لتصديرها. تأكد من وجود سائقين في القائمة.", "تنبيه",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                    {
                        Filter = "Excel Files (*.xlsx)|*.xlsx",
                        DefaultExt = "xlsx",
                        FileName = $"بيانات_السائقين_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx"
                    };

                    if (saveFileDialog.ShowDialog() == true)
                    {
                        System.Diagnostics.Debug.WriteLine($"📊 Exporting {viewModel.FilteredDrivers.Count} drivers to: {saveFileDialog.FileName}");

                        // تأكد من امتداد Excel
                        string filePath = saveFileDialog.FileName;
                        if (!filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
                            filePath = Path.ChangeExtension(filePath, ".xlsx");

                        ExportToExcel(viewModel.FilteredDrivers, filePath);

                        var result = MessageBox.Show($"تم تصدير {viewModel.FilteredDrivers.Count} سائق بنجاح إلى:\n{filePath}\n\nهل تريد فتح الملف الآن؟",
                                      "تصدير ناجح", MessageBoxButton.YesNo, MessageBoxImage.Information);

                        if (result == MessageBoxResult.Yes)
                        {
                            try
                            {
                                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                                {
                                    FileName = filePath,
                                    UseShellExecute = true
                                });
                            }
                            catch (Exception openEx)
                            {
                                MessageBox.Show($"تم حفظ الملف بنجاح ولكن لا يمكن فتحه تلقائياً:\n{openEx.Message}",
                                              "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewDriverButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Driver driver)
                {
                    var details = $"📋 تفاصيل السائق\n\n" +
                                 $"🆔 الرقم: {driver.Id}\n" +
                                 $"👤 الاسم: {driver.Name}\n" +
                                 $"📱 الهاتف: {driver.PhoneNumber}\n" +
                                 $"🆔 رقم البطاقة: {driver.CardNumber}\n" +
                                 $"📄 نوع البطاقة: {driver.CardType}\n" +
                                 $"📍 مكان الإصدار: {driver.CardIssuePlace}\n" +
                                 $"📅 تاريخ إصدار البطاقة: {driver.CardIssueDate:yyyy-MM-dd}\n" +
                                 $"🚗 نوع المركبة: {driver.VehicleType}\n" +
                                 $"🔢 رقم المركبة: {driver.VehicleNumber}\n" +
                                 $"🎨 لون المركبة: {driver.VehicleColor}\n" +
                                 $"⚡ قدرة المركبة: {driver.VehicleCapacity}\n" +
                                 $"🏷️ موديل المركبة: {driver.VehicleModel}\n" +
                                 $"🪪 رقم الرخصة: {driver.LicenseNumber}\n" +
                                 $"📅 تاريخ إصدار الرخصة: {driver.LicenseIssueDate:yyyy-MM-dd}\n" +
                                 $"📝 كود السائق: {driver.DriverCode}\n" +
                                 $"📊 الحالة: {driver.Status}\n" +
                                 $"📝 ملاحظات: {driver.Notes}";

                    MessageBox.Show(details, "تفاصيل السائق", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض التفاصيل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditDriverButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Driver driver)
                {
                    if (DataContext is VehicleManagementViewModel viewModel)
                    {
                        viewModel.SelectedDriver = driver;
                        MessageBox.Show($"تم تحديد السائق: {driver.Name}\nيمكنك الآن تعديل البيانات في النموذج أعلاه والضغط على 'حفظ التعديلات'",
                                      "تحديد للتعديل", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديد السائق للتعديل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeleteSelectedDriverButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel && viewModel.SelectedDriver != null)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف السائق: {viewModel.SelectedDriver.Name}؟\nهذا الإجراء لا يمكن التراجع عنه.",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        if (viewModel.DeleteCommand?.CanExecute() == true)
                        {
                            viewModel.DeleteCommand.Execute();
                            MessageBox.Show("تم حذف السائق بنجاح", "حذف ناجح", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار سائق من القائمة أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف السائق: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settingsMenu = new System.Windows.Controls.ContextMenu();

                var refreshMenuItem = new System.Windows.Controls.MenuItem
                {
                    Header = "🔄 تحديث البيانات",
                    ToolTip = "إعادة تحميل جميع البيانات من قاعدة البيانات"
                };
                refreshMenuItem.Click += RefreshDataButton_Click;

                var exportMenuItem = new System.Windows.Controls.MenuItem
                {
                    Header = "📊 تصدير إلى Excel",
                    ToolTip = "تصدير البيانات المعروضة إلى ملف Excel"
                };
                exportMenuItem.Click += ExportToExcelButton_Click;

                var clearFilterMenuItem = new System.Windows.Controls.MenuItem
                {
                    Header = "🔍 مسح الفلاتر",
                    ToolTip = "إزالة جميع الفلاتر وعرض كامل البيانات"
                };
                clearFilterMenuItem.Click += (s, args) => {
                    if (DataContext is VehicleManagementViewModel vm)
                    {
                        vm.ClearFilterCommand?.Execute();
                    }
                };

                settingsMenu.Items.Add(refreshMenuItem);
                settingsMenu.Items.Add(exportMenuItem);
                settingsMenu.Items.Add(new System.Windows.Controls.Separator());
                settingsMenu.Items.Add(clearFilterMenuItem);

                settingsMenu.IsOpen = true;
                settingsMenu.PlacementTarget = sender as Button;
                settingsMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.Top;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// فتح تبويب إضافة سائق جديد
        /// </summary>
        private void AddNewDriverButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel)
                {
                    // مسح النموذج للإدخال الجديد
                    viewModel.SelectedDriver = new Driver();

                    // البحث عن TabControl والتبديل إلى التبويب الثاني
                    var tabControl = FindName("MainTabControl") as TabControl;
                    if (tabControl == null)
                    {
                        // البحث في الشجرة المرئية
                        tabControl = FindVisualChild<TabControl>(this);
                    }

                    if (tabControl != null && tabControl.Items.Count > 1)
                    {
                        tabControl.SelectedIndex = 1; // التبويب الثاني (إضافة سائق)
                    }

                    // تحديث الإحصائيات
                    UpdateQuickStats();

                    MessageBox.Show(
                        "✅ تم تجهيز النموذج لإضافة سائق جديد\n\n" +
                        "📝 املأ البيانات في تبويب 'إضافة سائق'\n" +
                        "💾 اضغط 'حفظ البيانات' عند الانتهاء",
                        "جاهز للإدخال",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تجهيز نموذج السائق الجديد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث الإحصائيات السريعة في الهيدر
        /// </summary>
        private void UpdateQuickStats()
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel)
                {
                    var totalDrivers = viewModel.FilteredDrivers?.Count ?? 0;
                    var activeDrivers = viewModel.FilteredDrivers?.Count(d => d.IsActive) ?? 0;

                    // يمكن إضافة عرض الإحصائيات لاحقاً
                    System.Diagnostics.Debug.WriteLine($"📊 المجموع: {totalDrivers} | النشط: {activeDrivers}");
                }
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء الإحصائيات
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        /// <summary>
        /// فتح شاشة إدارة السائقين الاحترافية
        /// </summary>
        private void OpenProfessionalDriverManagementButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🚗 Opening Professional Driver Management Window");

                // فتح شاشة إدارة السائقين الاحترافية
                var driverManagementWindow = new Views.ProfessionalDriverManagementWindow();
                var result = driverManagementWindow.ShowDialog();

                if (result == true)
                {
                    // تحديث البيانات بعد إغلاق الشاشة
                    System.Diagnostics.Debug.WriteLine("✅ Driver management completed, refreshing data");

                    if (DataContext is VehicleManagementViewModel vm)
                    {
                        vm.LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error opening professional driver management: {ex.Message}");
                MessageBox.Show($"❌ خطأ في فتح شاشة إدارة السائقين:\n\n{ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إدراج البيانات الأولية للسائقين
        /// </summary>
        private async void ImportInitialDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل أنت متأكد من إدراج البيانات الأولية للسائقين؟\n\n" +
                    "سيتم إضافة 31 سائق مع جميع بياناتهم الكاملة.\n" +
                    "هذه العملية لن تؤثر على البيانات الموجودة مسبقاً.",
                    "تأكيد إدراج البيانات",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إظهار مؤشر التحميل
                    var loadingWindow = new Window
                    {
                        Title = "جاري إدراج البيانات...",
                        Width = 300,
                        Height = 150,
                        WindowStartupLocation = WindowStartupLocation.CenterOwner,
                        Owner = this,
                        ResizeMode = ResizeMode.NoResize,
                        Content = new StackPanel
                        {
                            Margin = new Thickness(20),
                            Children =
                            {
                                new TextBlock
                                {
                                    Text = "🔄 جاري إدراج بيانات السائقين...",
                                    FontSize = 14,
                                    HorizontalAlignment = HorizontalAlignment.Center,
                                    Margin = new Thickness(0, 20, 0, 20)
                                },
                                new ProgressBar
                                {
                                    IsIndeterminate = true,
                                    Height = 20
                                }
                            }
                        }
                    };

                    loadingWindow.Show();

                    try
                    {
                        // إدراج البيانات
                        using var context = new Data.ApplicationDbContext();
                        await Data.SeedDriversData.SeedDriversAsync(context);

                        loadingWindow.Close();

                        // إعادة تحميل البيانات
                        if (DataContext is VehicleManagementViewModel viewModel)
                        {
                            viewModel.LoadData();
                        }

                        MessageBox.Show(
                            "✅ تم إدراج بيانات السائقين بنجاح!\n\n" +
                            "تم إضافة 31 سائق مع جميع بياناتهم الكاملة.\n" +
                            "يمكنك الآن رؤية البيانات في الجدول.",
                            "نجح الإدراج",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        loadingWindow.Close();

                        if (ex.Message.Contains("يوجد") && ex.Message.Contains("سائق في قاعدة البيانات"))
                        {
                            MessageBox.Show(
                                "ℹ️ البيانات موجودة بالفعل!\n\n" +
                                "يوجد سائقين في قاعدة البيانات مسبقاً.\n" +
                                "لن يتم إضافة بيانات مكررة.",
                                "البيانات موجودة",
                                MessageBoxButton.OK,
                                MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show($"❌ خطأ في إدراج البيانات: {ex.Message}", "خطأ",
                                          MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عملية الإدراج: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportToExcel(ObservableCollection<Driver> drivers, string filePath)
        {
            try
            {
                // Note: EPPlus requires license for commercial use

                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("بيانات السائقين");

                    // Headers in Arabic - matching DataGrid columns exactly
                    var headers = new[]
                    {
                        "م", "اسم السائق", "رقم الهاتف", "نوع المركبة", "رقم المركبة",
                        "الحالة", "رقم البطاقة", "نوع البطاقة", "مكان إصدار البطاقة",
                        "تاريخ إصدار البطاقة", "رقم الرخصة", "تاريخ إصدار الرخصة",
                        "تاريخ انتهاء الرخصة", "رقم رخصة القيادة", "تاريخ انتهاء رخصة القيادة",
                        "لون المركبة", "قدرة المركبة", "موديل المركبة", "كود السائق", "ملاحظات"
                    };

                    // Add headers
                    for (int i = 0; i < headers.Length; i++)
                    {
                        worksheet.Cells[1, i + 1].Value = headers[i];
                    }

                    // Style headers
                    var headerRange = worksheet.Cells[1, 1, 1, headers.Length];
                    headerRange.Style.Font.Bold = true;
                    headerRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
                    headerRange.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
                    headerRange.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                    headerRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                    // Add data rows
                    int row = 2;
                    int rowNumber = 1;
                    foreach (var driver in drivers)
                    {
                        worksheet.Cells[row, 1].Value = rowNumber;
                        worksheet.Cells[row, 2].Value = driver.Name ?? "";
                        worksheet.Cells[row, 3].Value = driver.PhoneNumber ?? "";
                        worksheet.Cells[row, 4].Value = driver.VehicleType ?? "";
                        worksheet.Cells[row, 5].Value = driver.VehicleNumber ?? "";
                        worksheet.Cells[row, 6].Value = driver.Status ?? "";
                        worksheet.Cells[row, 7].Value = driver.CardNumber ?? "";
                        worksheet.Cells[row, 8].Value = driver.CardType ?? "";
                        worksheet.Cells[row, 9].Value = driver.CardIssuePlace ?? "";
                        worksheet.Cells[row, 10].Value = driver.CardIssueDate.ToString("yyyy-MM-dd");
                        worksheet.Cells[row, 11].Value = driver.LicenseNumber ?? "";
                        worksheet.Cells[row, 12].Value = driver.LicenseIssueDate.ToString("yyyy-MM-dd");
                        worksheet.Cells[row, 13].Value = driver.LicenseExpiryDate?.ToString("yyyy-MM-dd") ?? "";
                        worksheet.Cells[row, 14].Value = driver.DrivingLicenseNumber ?? "";
                        worksheet.Cells[row, 15].Value = driver.DrivingLicenseExpiryDate?.ToString("yyyy-MM-dd") ?? "";
                        worksheet.Cells[row, 16].Value = driver.VehicleColor ?? "";
                        worksheet.Cells[row, 17].Value = driver.VehicleCapacity ?? "";
                        worksheet.Cells[row, 18].Value = driver.VehicleModel ?? "";
                        worksheet.Cells[row, 19].Value = driver.DriverCode ?? "";
                        worksheet.Cells[row, 20].Value = driver.Notes ?? "";

                        row++;
                        rowNumber++;
                    }

                    // Create table
                    var tableRange = worksheet.Cells[1, 1, row - 1, headers.Length];
                    var table = worksheet.Tables.Add(tableRange, "DriversTable");
                    table.TableStyle = TableStyles.Medium2;

                    // Auto-fit columns
                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                    // Set RTL for Arabic
                    worksheet.View.RightToLeft = true;

                    // Save file
                    var fileInfo = new FileInfo(filePath);
                    package.SaveAs(fileInfo);
                }

                System.Diagnostics.Debug.WriteLine($"✅ Successfully exported {drivers.Count} drivers to Excel: {filePath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Export error: {ex.Message}");
                throw new Exception($"فشل في إنشاء ملف Excel: {ex.Message}");
            }
        }



        private void AdvancedSearchButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("🔍 البحث المتقدم\n\nيمكنك استخدام مربعات البحث في أعلى الجدول للبحث السريع.\nسيتم إضافة المزيد من خيارات البحث قريباً.",
                              "البحث المتقدم", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void StatisticsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel)
                {
                    var totalDrivers = viewModel.FilteredDrivers.Count;
                    var activeDrivers = viewModel.FilteredDrivers.Count(d => d.Status == "نشط");
                    var inactiveDrivers = viewModel.FilteredDrivers.Count(d => d.Status == "غير نشط");
                    var vehicleTypes = viewModel.FilteredDrivers.GroupBy(d => d.VehicleType)
                                                               .Where(g => !string.IsNullOrEmpty(g.Key))
                                                               .Select(g => $"• {g.Key}: {g.Count()} سائق")
                                                               .ToList();

                    var statistics = $"📊 إحصائيات السائقين\n\n" +
                                   $"👥 إجمالي السائقين: {totalDrivers}\n" +
                                   $"✅ السائقين النشطين: {activeDrivers}\n" +
                                   $"❌ السائقين غير النشطين: {inactiveDrivers}\n\n" +
                                   $"🚗 توزيع أنواع المركبات:\n" +
                                   (vehicleTypes.Any() ? string.Join("\n", vehicleTypes.Take(8)) : "لا توجد بيانات") +
                                   (vehicleTypes.Count > 8 ? $"\n... و {vehicleTypes.Count - 8} نوع آخر" : "");

                    MessageBox.Show(statistics, "إحصائيات مفصلة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض الإحصائيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// مسح نص البحث
        /// </summary>
        private void ClearSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel)
                {
                    viewModel.SearchText = string.Empty;
                    SearchTextBox.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في مسح البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// مسح جميع الفلاتر
        /// </summary>
        private void ClearFilter_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel)
                {
                    viewModel.SearchText = string.Empty;
                    viewModel.SelectedVehicleTypeFilter = "الكل";
                    viewModel.SelectedVehicleCapacityFilter = "الكل";
                    SearchTextBox.Focus();

                    MessageBox.Show("✅ تم مسح جميع الفلاتر بنجاح", "مسح الفلاتر",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في مسح الفلاتر: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// مسح النموذج في تبويب إضافة السائق
        /// </summary>
        private void ClearFormButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel)
                {
                    var result = MessageBox.Show(
                        "هل تريد مسح جميع البيانات المدخلة في النموذج؟",
                        "تأكيد المسح",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        viewModel.SelectedDriver = new Driver();
                        MessageBox.Show(
                            "✅ تم مسح النموذج بنجاح",
                            "تم المسح",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في مسح النموذج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// الانتقال إلى تبويب قائمة السائقين
        /// </summary>
        private void ShowDriversListButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var tabControl = FindName("MainTabControl") as TabControl;
                if (tabControl == null)
                {
                    tabControl = FindVisualChild<TabControl>(this);
                }

                if (tabControl != null && tabControl.Items.Count > 0)
                {
                    tabControl.SelectedIndex = 0; // التبويب الأول (قائمة السائقين)
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الانتقال إلى القائمة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// البحث عن عنصر في الشجرة المرئية
        /// </summary>
        private T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }
    }
}
